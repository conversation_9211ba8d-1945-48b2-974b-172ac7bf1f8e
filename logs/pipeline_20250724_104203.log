2025-07-24 10:42:03,742 - INFO - ======================================================================
2025-07-24 10:42:03,742 - INFO - FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025
2025-07-24 10:42:03,742 - INFO - ======================================================================
2025-07-24 10:42:03,742 - INFO - Hardware: 96 CPU cores, 1007.1GB RAM
2025-07-24 10:42:03,742 - INFO - GPU: 2 GPUs, 90.0GB VRAM
2025-07-24 10:42:03,742 - INFO - Paralelizace: 32 workers
2025-07-24 10:42:03,743 - INFO - Načít<PERSON>í dat...
2025-07-24 10:42:04,204 - INFO - Data ú<PERSON><PERSON>šně načtena. Trénovací: (6416, 4, 5000), Test: (3200, 4, 5000)
2025-07-24 10:42:04,204 - INFO - Extrakce příznaků...
2025-07-24 10:42:05,644 - INFO - Načítám příznaky z cache: cache/train_features_241b14a1f2a5e21b.npy
2025-07-24 10:42:05,648 - INFO - Načteny příznaky z cache: (6416, 131)
2025-07-24 10:42:06,352 - INFO - Načítám příznaky z cache: cache/test_features_03f2eab8e0d3024c.npy
2025-07-24 10:42:06,354 - INFO - Načteny příznaky z cache: (3200, 131)
2025-07-24 10:42:06,355 - INFO - Dimenze příznaků: 131 příznaků na vzorek
2025-07-24 10:42:06,355 - INFO - Aplikuji SMOTE-R augmentaci...
2025-07-24 10:42:06,376 - INFO - Augmentace dokončena: 6416 -> 8001 vzorků
2025-07-24 10:42:06,376 - INFO - Cross-validace finálního pipeline...
2025-07-24 10:42:06,377 - INFO - Používám 32 CPU cores pro paralelizaci
2025-07-24 10:42:06,378 - INFO - Pipeline vytvořen s hardware optimalizací
2025-07-24 10:55:37,085 - ERROR - Chyba při cross-validaci: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/sklearn/model_selection/_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/sklearn/base.py", line 1389, in wrapper
    return fit_method(estimator, *args, **kwargs)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/sklearn/pipeline.py", line 662, in fit
    self._final_estimator.fit(Xt, y, **last_step_params["fit"])
  File "/home/<USER>/Loziska/final_optimized_pipeline.py", line 451, in fit
    pred = est.predict(X_val)
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/sklearn.py", line 1248, in predict
    predts = self.get_booster().inplace_predict(
  File "/home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/sklearn.py", line 853, in get_booster
    raise NotFittedError("need to call fit or load_model beforehand")
sklearn.exceptions.NotFittedError: need to call fit or load_model beforehand

