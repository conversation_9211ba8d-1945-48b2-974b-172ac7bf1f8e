"""
Finální optimalizovaný pipeline kombinující nejlepší přístupy
pro Ball Bearing Predictive Challenge 2025.

Vylepšení v této verzi:
- Zohlednění nekonstantního (ne-stacionárního) spektra signálů.
- <PERSON>k<PERSON> č<PERSON>ě-proměnlivých statistik pomocí windowingu (chunking).
- Extrakce příznaků ze spektrogramu (STFT) pro zachycení dynamiky.
- Vylepšená a agresivnější augmentace dat (SMOTE-R).
- Kalibrace predikcí s lineární extrapolací pro lepší pokrytí celého rozsahu.
- Optimalizované a vyladěné hyperparametry pro XGBoost s early stopping.
"""

import numpy as np
import pandas as pd
import pickle
import os
import sys
import signal
import time
import logging
import psutil
import hashlib
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy.stats import skew, kurtosis
from scipy.signal import welch, find_peaks, stft
import pywt

# Import modelů a nástrojů
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, WhiteKernel
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import make_scorer, mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin

import xgboost as xgb
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')

# --- Globální proměnné a nastavení (stejné jako u vás) ---
shutdown_requested = False
current_process = None

def setup_logging():
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"pipeline_final_{timestamp}.log"
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)])
    return logging.getLogger(__name__)

def signal_handler(signum, frame):
    global shutdown_requested
    shutdown_requested = True
    logging.getLogger(__name__).warning(f"Přijat signal {signum}. Zahajuji graceful shutdown...")
    sys.exit(0)

def setup_signal_handlers():
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

# --- Konfigurace a pomocné třídy (stejné jako u vás) ---
class Config:
    def __init__(self):
        self.data_path = 'student_data.pkl' # Ujistěte se, že cesta je správná
        self.cache_dir = Path('cache')
        self.results_dir = Path('results')
        self.n_jobs = max(1, psutil.cpu_count(logical=True) - 4)
        self.batch_size = 512
        self.augment_factor = 5 # Agresivnější augmentace
        self.noise_std = 50
        self.cv_folds = 5
        self.random_state = 42
        self.cache_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)

class DataCache:
    def __init__(self, cache_dir):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    def _get_hash(self, data): return hashlib.md5(data.tobytes()).hexdigest()
    def load(self, data, name):
        path = self.cache_dir / f"{name}_{self._get_hash(data)}.pkl"
        if path.exists(): return joblib.load(path)
        return None
    def save(self, obj, data, name):
        path = self.cache_dir / f"{name}_{self._get_hash(data)}.pkl"
        joblib.dump(obj, path)

class SmoteRTransformer(BaseEstimator, TransformerMixin):
    def __init__(self, discrete_levels=None, augment_factor=5, noise_std=50):
        self.discrete_levels = discrete_levels or [2500, 7500, 12500, 17500]
        self.augment_factor = augment_factor
        self.noise_std = noise_std
    def fit(self, X, y=None): return self
    def transform(self, X): return X
    def fit_resample(self, X, y):
        X_aug, y_aug = [X], [y]
        logging.info(f"Aplikuji SMOTE-R augmentaci (faktor: {self.augment_factor})...")
        for _ in tqdm(range(self.augment_factor), desc="Augmentace"):
            if shutdown_requested: break
            X_new_gen, y_new_gen = [], []
            current_y_pool, current_X_pool = np.concatenate(y_aug), np.vstack(X_aug)
            for i in range(len(self.discrete_levels) - 1):
                level1, level2 = self.discrete_levels[i], self.discrete_levels[i+1]
                idx1 = np.where(np.abs(current_y_pool - level1) < 500)[0]
                idx2 = np.where(np.abs(current_y_pool - level2) < 500)[0]
                if len(idx1) == 0 or len(idx2) == 0: continue
                n_synthetic = min(len(idx1), len(idx2))
                pairs1 = np.random.choice(idx1, size=n_synthetic, replace=True)
                pairs2 = np.random.choice(idx2, size=n_synthetic, replace=True)
                for i1, i2 in zip(pairs1, pairs2):
                    alpha = np.random.normal(0.5, 0.15); alpha = np.clip(alpha, 0.05, 0.95)
                    X_synth = (1 - alpha) * current_X_pool[i1] + alpha * current_X_pool[i2]
                    y_synth = (1 - alpha) * current_y_pool[i1] + alpha * current_y_pool[i2]
                    y_synth += np.random.normal(0, self.noise_std)
                    X_new_gen.append(X_synth); y_new_gen.append(y_synth)
            if X_new_gen: X_aug.append(np.array(X_new_gen)); y_aug.append(np.array(y_new_gen))
        X_final, y_final = np.vstack(X_aug), np.concatenate(y_aug)
        logging.info(f"Augmentace dokončena: {len(X)} -> {len(X_final)} vzorků")
        return X_final, y_final

class CalibratedPredictor:
    def __init__(self, pipeline, isotonic):
        self.pipeline = pipeline; self.isotonic = isotonic
        self.min_pred, self.max_pred = np.min(isotonic.X_thresholds_), np.max(isotonic.X_thresholds_)
        self.min_val, self.max_val = np.min(isotonic.y_thresholds_), np.max(isotonic.y_thresholds_)
    def predict(self, X):
        raw_pred = self.pipeline.predict(X)
        calibrated_pred = self.isotonic.predict(raw_pred)
        extrapolated = np.copy(calibrated_pred)
        mask_low = raw_pred < self.min_pred
        extrapolated[mask_low] = self.min_val + (raw_pred[mask_low] - self.min_pred)
        mask_high = raw_pred > self.max_pred
        extrapolated[mask_high] = self.max_val + (raw_pred[mask_high] - self.max_pred)
        return np.clip(extrapolated, 0, 19200)

# --- NOVÁ FUNKCE PRO EXTRAKCI PŘÍZNAKŮ ---
def extract_features_final(sample, fs=1.5625e6, n_chunks=10):
    all_features = []
    
    for channel_data in sample:
        # === 1. Globální časové příznaky ===
        rms = np.sqrt(np.mean(channel_data**2))
        all_features.extend([
            np.mean(channel_data), np.std(channel_data), skew(channel_data),
            kurtosis(channel_data), np.ptp(channel_data), rms,
            np.max(np.abs(channel_data)) / (rms + 1e-9) # Crest Factor
        ])
        
        # === 2. Časově-proměnlivé statistiky (Chunking) ===
        chunks = np.array_split(channel_data, n_chunks)
        chunk_means = [np.mean(c) for c in chunks]
        chunk_stds = [np.std(c) for c in chunks]
        chunk_kurtosis = [kurtosis(c) for c in chunks]
        all_features.extend([
            np.mean(chunk_stds), np.std(chunk_stds), # Jak se mění rozptyl v čase
            np.mean(chunk_kurtosis), np.std(chunk_kurtosis) # Jak se mění špičatost
        ])
        
        # === 3. Příznaky ze Spektrogramu (STFT) ===
        f, t, Zxx = stft(channel_data, fs=fs, nperseg=256, noverlap=128)
        Sxx = np.abs(Zxx)
        
        # Statistiky celého spektrogramu
        all_features.extend([np.mean(Sxx), np.std(Sxx), skew(Sxx.flatten()), kurtosis(Sxx.flatten())])
        
        # Dynamika energie v čase (změna sloupce spektrogramu)
        energy_envelope = np.sum(Sxx, axis=0)
        all_features.append(np.std(energy_envelope))

        # Průměrné spektrum (zprůměrování řádků)
        mean_spectrum = np.mean(Sxx, axis=1)
        if np.sum(mean_spectrum) > 0:
            spectral_centroid = np.sum(f * mean_spectrum) / np.sum(mean_spectrum)
        else:
            spectral_centroid = 0
        all_features.append(spectral_centroid)

        # === 4. Příznaky z Waveletů (časově-frekvenční) ===
        coeffs = pywt.wavedec(channel_data, 'db4', level=6)
        for level_coeffs in coeffs:
            all_features.extend([np.mean(np.abs(level_coeffs)), np.std(level_coeffs), np.sum(level_coeffs**2)])

    # === 5. Mezi-kanálové příznaky ===
    corr_matrix = np.corrcoef(sample)
    all_features.extend(corr_matrix[np.triu_indices(4, k=1)])
    
    return np.array(all_features)


def process_in_batches(data, func, cache, name, batch_size):
    cached_data = cache.load(data, name)
    if cached_data is not None:
        logging.info(f"Načteno z cache '{name}': {cached_data.shape}")
        return cached_data
    
    logging.info(f"Zpracovávám '{name}' v dávkách...")
    results = [func(s) for s in tqdm(data, desc=name)]
    results = np.array(results)
    
    cache.save(results, data, name)
    logging.info(f"Zpracování '{name}' dokončeno: {results.shape}")
    return results

def create_final_pipeline(config, use_early_stopping=False):
    model_params = {
        'n_estimators': 2000, 'learning_rate': 0.02, 'max_depth': 10,
        'subsample': 0.7, 'colsample_bytree': 0.7, 'gamma': 0.05,
        'reg_alpha': 0.05, 'random_state': config.random_state,
        'n_jobs': config.n_jobs, 'tree_method': 'hist', 'verbosity': 0
    }
    if use_early_stopping:
        model_params.update({'early_stopping_rounds': 50, 'eval_metric': 'mae'})
    
    model = xgb.XGBRegressor(**model_params)
    return Pipeline([('scaler', RobustScaler()), ('model', model)])


def main():
    setup_signal_handlers()
    logger = setup_logging()
    config = Config()

    logger.info("="*70 + "\nFINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025\n" + "="*70)
    logger.info(f"Paralelizace: {config.n_jobs} workers")

    cache = DataCache(config.cache_dir)
    
    logger.info("Načítání dat...")
    try:
        with open(config.data_path, 'rb') as f: data = pickle.load(f)
        X_train_raw, y_train, X_test_raw = data['train_dataset'], data['train_response'], data['test_dataset']
    except Exception as e:
        logger.error(f"Chyba při načítání dat: {e}"); return

    if shutdown_requested: return
    
    X_train = process_in_batches(X_train_raw, extract_features_final, cache, "train_features_v2", config.batch_size)
    if shutdown_requested: return
    X_test = process_in_batches(X_test_raw, extract_features_final, cache, "test_features_v2", config.batch_size)
    
    if np.any(np.isnan(X_train)) or np.any(np.isinf(X_train)):
        X_train = np.nan_to_num(X_train)
    if np.any(np.isnan(X_test)) or np.any(np.isinf(X_test)):
        X_test = np.nan_to_num(X_test)

    if shutdown_requested: return
    
    smoter = SmoteRTransformer(augment_factor=config.augment_factor, noise_std=config.noise_std)
    X_train_aug, y_train_aug = smoter.fit_resample(X_train, y_train)

    if shutdown_requested: return

    logger.info("Cross-validace finálního pipeline...")
    cv_splitter = KFold(n_splits=config.cv_folds, shuffle=True, random_state=config.random_state)
    pipeline_cv = create_final_pipeline(config, use_early_stopping=False) # Early stopping se v CV používá složitěji
    
    try:
        cv_scores = cross_val_score(pipeline_cv, X_train_aug, y_train_aug,
                                    cv=cv_splitter, scoring='neg_mean_absolute_error', n_jobs=config.n_jobs)
        logger.info(f"Cross-validace MAE: {-np.mean(cv_scores):.2f} ± {np.std(cv_scores):.2f} g")
    except Exception as e:
        logger.error(f"Chyba při cross-validaci: {e}")

    if shutdown_requested: return

    logger.info("Trénování finálního modelu na všech datech s early stopping...")
    X_train_final, X_val, y_train_final, y_val = train_test_split(
        X_train_aug, y_train_aug, test_size=0.1, random_state=config.random_state
    )
    pipeline_final = create_final_pipeline(config, use_early_stopping=True)
    scaler = pipeline_final.named_steps['scaler']
    X_val_scaled = scaler.fit(X_train_final).transform(X_val) # Scaler se musí fitnout jen na trénovací části
    
    try:
        start_time = time.time()
        pipeline_final.fit(X_train_final, y_train_final,
                           model__eval_set=[(X_val_scaled, y_val)], model__verbose=False)
        logger.info(f"Trénování dokončeno za {time.time() - start_time:.1f}s")
        logger.info(f"Nejlepší iterace (Early Stopping): {pipeline_final.named_steps['model'].best_iteration}")
    except Exception as e:
        logger.error(f"Chyba při trénování: {e}"); return
    
    if shutdown_requested: return
    
    logger.info("Kalibrace pomocí Isotonic Regression...")
    train_pred = pipeline_final.predict(X_train)
    isotonic = IsotonicRegression(out_of_bounds='clip', y_min=0, y_max=19200)
    isotonic.fit(train_pred, y_train)
    
    final_predictor = CalibratedPredictor(pipeline_final, isotonic)
    
    logger.info("Vytváření finálních predikcí...")
    final_predictions = final_predictor.predict(X_test)
    
    logger.info("Ukládání výsledků...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = config.results_dir / timestamp
    results_dir.mkdir(exist_ok=True)
    np.save(results_dir / 'predictions_final.npy', final_predictions)
    joblib.dump(final_predictor, results_dir / 'final_predictor.pkl')
    logger.info(f"Výsledky uloženy do: {results_dir}")
    
    logger.info("="*70 + "\nPIPELINE ÚSPĚŠNĚ DOKONČEN!\n" + "="*70)

if __name__ == "__main__":
    main()