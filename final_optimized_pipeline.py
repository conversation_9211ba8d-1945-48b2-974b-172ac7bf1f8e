"""
Finální optimalizovaný pipeline kombinující nejlepší přístupy
pro Ball Bearing Predictive Challenge 2025.

Vylepšení:
- Správné signal handling pro graceful shutdown
- Inteligentní data loading s cachováním
- Optimalizace pro dostupný hardware (2x L40S GPU, 96 CPU cores)
- Pokročilé logování a progress tracking
- Memory management a batch processing
- Konfigurovatelné parametry
"""

import numpy as np
import pandas as pd
import pickle
import os
import sys
import signal
import time
import logging
import psutil
import hashlib
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy.stats import skew, kurtosis
from scipy.signal import welch, find_peaks
from scipy.interpolate import UnivariateSpline

# Import modelů a nástrojů
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, WhiteKernel
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import make_scorer, mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin

import xgboost as xgb
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')

# Globální proměnné pro signal handling
shutdown_requested = False
current_process = None

def setup_logging():
    """Nastavení pokročilého logování."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"pipeline_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def signal_handler(signum, frame):
    """Handler pro graceful shutdown při Ctrl+C."""
    global shutdown_requested, current_process
    shutdown_requested = True
    logger = logging.getLogger(__name__)
    logger.warning(f"Přijat signal {signum}. Zahajuji graceful shutdown...")

    if current_process:
        logger.info("Ukončuji aktuální proces...")
        try:
            current_process.terminate()
        except:
            pass

    logger.info("Cleanup dokončen. Ukončuji program.")
    sys.exit(0)

def setup_signal_handlers():
    """Nastavení signal handlerů."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def get_hardware_info():
    """Získá informace o dostupném hardware."""
    info = {
        'cpu_count': psutil.cpu_count(logical=True),
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'gpu_count': 0,
        'gpu_memory_gb': 0
    }

    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        info['gpu_count'] = len(gpus)
        if gpus:
            info['gpu_memory_gb'] = sum(gpu.memoryTotal for gpu in gpus) / 1024
    except ImportError:
        # Fallback na nvidia-smi
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=count,memory.total',
                                   '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info['gpu_count'] = len(lines)
                info['gpu_memory_gb'] = sum(int(line.split(',')[1]) for line in lines) / 1024
        except:
            pass

    return info

class Config:
    """Konfigurace pipeline."""
    def __init__(self):
        # Cesty k souborům
        self.data_path = 'AMSM_LOZISKA_2025/student_data.pkl'
        self.cache_dir = Path('cache')
        self.results_dir = Path('results')

        # Hardware optimalizace
        hw_info = get_hardware_info()
        self.n_jobs = min(hw_info['cpu_count'] - 4, 32)  # Ponechat 4 cores pro systém
        self.use_gpu = hw_info['gpu_count'] > 0

        # Data processing
        self.batch_size = 1000  # Pro feature extraction
        self.augment_factor = 3
        self.noise_std = 50

        # Model parametry
        self.cv_folds = 5
        self.random_state = 42

        # Memory management
        self.max_memory_usage = 0.8  # 80% max memory usage

        # Vytvoření adresářů
        self.cache_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)

print("Všechny knihovny úspěšně naimportovány.")


class DataCache:
    """Správa cache pro extrahované příznaky."""

    def __init__(self, cache_dir):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)

    def _get_data_hash(self, data):
        """Vytvoří hash z dat pro identifikaci cache."""
        if isinstance(data, np.ndarray):
            return hashlib.md5(data.tobytes()).hexdigest()[:16]
        else:
            return hashlib.md5(str(data).encode()).hexdigest()[:16]

    def get_cache_path(self, data, feature_type="features"):
        """Získá cestu k cache souboru."""
        data_hash = self._get_data_hash(data)
        return self.cache_dir / f"{feature_type}_{data_hash}.npy"

    def load_features(self, data, feature_type="features"):
        """Načte příznaky z cache pokud existují."""
        cache_path = self.get_cache_path(data, feature_type)
        if cache_path.exists():
            self.logger.info(f"Načítám příznaky z cache: {cache_path}")
            return np.load(cache_path)
        return None

    def save_features(self, features, data, feature_type="features"):
        """Uloží příznaky do cache."""
        cache_path = self.get_cache_path(data, feature_type)
        np.save(cache_path, features)
        self.logger.info(f"Příznaky uloženy do cache: {cache_path}")

class MemoryMonitor:
    """Monitoring paměti během zpracování."""

    def __init__(self, max_usage=0.8):
        self.max_usage = max_usage
        self.logger = logging.getLogger(__name__)

    def check_memory(self):
        """Zkontroluje aktuální využití paměti."""
        memory = psutil.virtual_memory()
        usage = memory.percent / 100

        if usage > self.max_usage:
            self.logger.warning(f"Vysoké využití paměti: {usage:.1%}")
            return False
        return True

    def force_gc(self):
        """Vynutí garbage collection."""
        import gc
        gc.collect()
        self.logger.info("Garbage collection dokončen")

class SmoteRTransformer(BaseEstimator, TransformerMixin):
    """SMOTE pro regresi - augmentace dat mezi diskrétními úrovněmi."""

    def __init__(self, discrete_levels=None, augment_factor=2, noise_std=50):
        self.discrete_levels = discrete_levels or [2500, 7500, 12500, 17500]
        self.augment_factor = augment_factor
        self.noise_std = noise_std
        self.logger = logging.getLogger(__name__)

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return X

    def fit_resample(self, X, y):
        """Vytvoří syntetické vzorky mezi diskrétními úrovněmi."""
        global shutdown_requested

        X_aug = [X]
        y_aug = [y]

        self.logger.info("Aplikuji SMOTE-R augmentaci...")
        for i in range(len(self.discrete_levels) - 1):
            if shutdown_requested:
                self.logger.warning("Shutdown požadován, ukončuji augmentaci")
                break

            level1, level2 = self.discrete_levels[i], self.discrete_levels[i+1]

            idx1 = np.where(np.abs(y - level1) < 100)[0]
            idx2 = np.where(np.abs(y - level2) < 100)[0]

            if len(idx1) > 0 and len(idx2) > 0:
                n_synthetic = min(len(idx1), len(idx2)) // self.augment_factor

                for _ in range(n_synthetic):
                    if shutdown_requested:
                        break

                    # Náhodný výběr
                    i1, i2 = np.random.choice(idx1), np.random.choice(idx2)

                    # Interpolace s náhodným faktorem
                    alpha = np.random.uniform(0.2, 0.8)

                    # Syntetický vzorek
                    X_synth = (1 - alpha) * X[i1] + alpha * X[i2]
                    y_synth = (1 - alpha) * level1 + alpha * level2
                    y_synth += np.random.normal(0, self.noise_std)

                    X_aug.append(X_synth.reshape(1, -1))
                    y_aug.append([y_synth])

        X_final = np.vstack(X_aug)
        y_final = np.concatenate(y_aug)
        self.logger.info(f"Augmentace dokončena: {len(X)} -> {len(X_final)} vzorků")

        return X_final, y_final


def extract_advanced_features(sample, fs=1.5625e6):
    """
    Pokročilá extrakce příznaků kombinující váš přístup s AE-specifickými příznaky.
    """
    all_features = []
    num_freq_bands = 10

    for ch_idx, channel_data in enumerate(sample):
        # === ČASOVÁ DOMÉNA (váš přístup + AE příznaky) ===
        # Základní statistiky
        all_features.extend([
            np.mean(channel_data),
            np.std(channel_data),
            np.var(channel_data),
            skew(channel_data),
            kurtosis(channel_data),
            np.max(channel_data),
            np.min(channel_data),
            np.max(channel_data) - np.min(channel_data),  # Peak-to-peak
        ])

        # RMS - kritický pro zatížení ložisek
        rms = np.sqrt(np.mean(channel_data**2))
        all_features.append(rms)

        # Crest Factor
        peak = np.max(np.abs(channel_data))
        crest_factor = peak / rms if rms > 0 else 0
        all_features.append(crest_factor)

        # AE-specifické příznaky
        # Hit rate - počet AE událostí
        threshold = 3 * np.std(channel_data)
        peaks, _ = find_peaks(np.abs(channel_data), height=threshold)
        hit_rate = len(peaks) / (len(channel_data) / fs)
        all_features.append(hit_rate)

        # Energy
        energy = np.sum(channel_data**2)
        all_features.append(np.log(energy + 1e-10))

        # === FREKVENČNÍ DOMÉNA ===
        fft_vals = np.abs(np.fft.rfft(channel_data))
        fft_freq = np.fft.rfftfreq(len(channel_data), 1/fs)

        all_features.extend([
            np.mean(fft_vals),
            np.std(fft_vals),
            skew(fft_vals),
            kurtosis(fft_vals)
        ])

        # Spektrální centroid - důležitý pro AE
        if np.sum(fft_vals) > 0:
            spectral_centroid = np.sum(fft_freq * fft_vals) / np.sum(fft_vals)
        else:
            spectral_centroid = 0
        all_features.append(spectral_centroid)

        # Energie ve frekvenčních pásmech (váš přístup)
        band_energies = np.array_split(fft_vals**2, num_freq_bands)
        for band in band_energies:
            all_features.append(np.mean(band))

        # Dodatečné AE pásma (0-10kHz, 10-50kHz, 50-100kHz, 100-200kHz, 200-500kHz)
        ae_bands = [(0, 10e3), (10e3, 50e3), (50e3, 100e3), (100e3, 200e3), (200e3, 500e3)]
        for low, high in ae_bands:
            mask = (fft_freq >= low) & (fft_freq < high)
            if np.any(mask):
                band_energy = np.sum(fft_vals[mask]**2)
                all_features.append(band_energy)
            else:
                all_features.append(0)

    # === MEZIKANÁLOVÉ PŘÍZNAKY ===
    # Průměrná korelace mezi kanály
    corr_matrix = np.corrcoef(sample)
    upper_triangle = corr_matrix[np.triu_indices(4, k=1)]
    all_features.extend([
        np.mean(upper_triangle),
        np.std(upper_triangle),
        np.max(upper_triangle)
    ])

    return np.array(all_features)

def extract_features_batch(data, cache, feature_type="features", batch_size=1000):
    """
    Extrakce příznaků s batch processingem a cachováním.
    """
    global shutdown_requested
    logger = logging.getLogger(__name__)
    memory_monitor = MemoryMonitor()

    # Zkusit načíst z cache
    cached_features = cache.load_features(data, feature_type)
    if cached_features is not None:
        logger.info(f"Načteny příznaky z cache: {cached_features.shape}")
        return cached_features

    logger.info(f"Extrakce příznaků z {len(data)} vzorků (batch_size={batch_size})")

    all_features = []
    n_batches = (len(data) + batch_size - 1) // batch_size

    for batch_idx in tqdm(range(n_batches), desc="Extrakce příznaků"):
        if shutdown_requested:
            logger.warning("Shutdown požadován, ukončuji extrakci příznaků")
            break

        # Kontrola paměti
        if not memory_monitor.check_memory():
            memory_monitor.force_gc()
            if not memory_monitor.check_memory():
                logger.error("Nedostatek paměti, snižuji batch_size")
                batch_size = max(batch_size // 2, 100)

        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(data))
        batch_data = data[start_idx:end_idx]

        # Extrakce příznaků pro batch
        batch_features = []
        for sample in batch_data:
            if shutdown_requested:
                break
            features = extract_advanced_features(sample)
            batch_features.append(features)

        if batch_features:
            all_features.extend(batch_features)

    if not all_features:
        logger.error("Žádné příznaky nebyly extrahovány")
        return None

    features_array = np.array(all_features)

    # Uložit do cache
    cache.save_features(features_array, data, feature_type)

    logger.info(f"Extrakce dokončena: {features_array.shape}")
    return features_array


class WeightedVotingRegressor(VotingRegressor):
    """Voting regressor s automatickou optimalizací vah."""

    def __init__(self, estimators, n_jobs=None):
        super().__init__(estimators=estimators, n_jobs=n_jobs)
        self.logger = logging.getLogger(__name__)

    def fit(self, X, y):
        global shutdown_requested

        if shutdown_requested:
            self.logger.warning("Shutdown požadován, přeskakuji trénování")
            return self

        # Rozdělení pro validaci vah
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        # Trénování base modelů
        self.logger.info("Trénuji base modely pro ensemble...")
        super().fit(X_train, y_train)

        if shutdown_requested:
            return self

        # Optimalizace vah podle validačního výkonu
        self.logger.info("Optimalizuji váhy ensemble...")
        val_scores = []
        for name, est in self.estimators_:
            if shutdown_requested:
                break
            pred = est.predict(X_val)
            mae = mean_absolute_error(y_val, pred)
            val_scores.append(1/mae)  # Inverzní MAE jako váha
            self.logger.info(f"Model {name}: MAE = {mae:.2f}g")

        # Normalizace vah
        total = sum(val_scores)
        self.weights = [s/total for s in val_scores]
        self.logger.info(f"Optimalizované váhy ensemble: {[f'{w:.3f}' for w in self.weights]}")

        # Přetrénování na všech datech
        if not shutdown_requested:
            self.logger.info("Přetrénovávám na všech datech...")
            return super().fit(X, y)

        return self


def create_final_pipeline(config):
    """Vytvoří finální pipeline s nejlepšími komponenty a hardware optimalizací."""
    logger = logging.getLogger(__name__)

    # Kernel pro GPR - Matérn je vhodný pro ne-nekonečně diferencovatelné funkce
    gpr_kernel = Matern(length_scale=1.0, nu=1.5) + WhiteKernel(noise_level=1)

    # Hardware-optimalizované parametry
    n_jobs = config.n_jobs
    logger.info(f"Používám {n_jobs} CPU cores pro paralelizaci")

    # Definice modelů s optimalizovanými parametry
    estimators = [
        ('xgb', xgb.XGBRegressor(
            n_estimators=400,  # Zvýšeno pro lepší výkon
            learning_rate=0.05,
            max_depth=8,
            subsample=0.8,
            colsample_bytree=0.8,
            gamma=0.1,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=config.random_state,
            n_jobs=n_jobs,
            tree_method='hist',  # Rychlejší pro velké datasety
            verbosity=0
        )),
        ('lgb', lgb.LGBMRegressor(
            n_estimators=400,
            learning_rate=0.05,
            num_leaves=50,
            max_depth=-1,
            min_child_samples=5,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=config.random_state,
            n_jobs=n_jobs,
            verbose=-1,
            force_col_wise=True  # Optimalizace pro široké datasety
        )),
        ('gpr', GaussianProcessRegressor(
            kernel=gpr_kernel,
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=3,
            random_state=config.random_state
        ))
    ]

    # Weighted voting ensemble
    ensemble = WeightedVotingRegressor(estimators=estimators, n_jobs=n_jobs)

    # Kompletní pipeline
    pipeline = Pipeline([
        ('scaler', RobustScaler()),  # Robustnější vůči outlierům
        ('ensemble', ensemble)
    ])

    logger.info("Pipeline vytvořen s hardware optimalizací")
    return pipeline


def main():
    """Hlavní funkce orchestrující celý proces."""
    global current_process, shutdown_requested

    # Nastavení signal handlers a logování
    setup_signal_handlers()
    logger = setup_logging()

    # Konfigurace
    config = Config()

    # Hardware info
    hw_info = get_hardware_info()
    logger.info("="*70)
    logger.info("FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025")
    logger.info("="*70)
    logger.info(f"Hardware: {hw_info['cpu_count']} CPU cores, {hw_info['memory_gb']:.1f}GB RAM")
    logger.info(f"GPU: {hw_info['gpu_count']} GPUs, {hw_info['gpu_memory_gb']:.1f}GB VRAM")
    logger.info(f"Paralelizace: {config.n_jobs} workers")

    # Inicializace cache a memory monitor
    cache = DataCache(config.cache_dir)
    memory_monitor = MemoryMonitor(config.max_memory_usage)

    # Načtení dat
    logger.info("Načítání dat...")
    try:
        with open(config.data_path, 'rb') as f:
            data = pickle.load(f)
        X_train_raw = data['train_dataset']
        y_train = data['train_response']
        X_test_raw = data['test_dataset']
        logger.info(f"Data úspěšně načtena. Trénovací: {X_train_raw.shape}, Test: {X_test_raw.shape}")
    except FileNotFoundError:
        logger.error(f"Chyba: Soubor '{config.data_path}' nebyl nalezen.")
        return
    except Exception as e:
        logger.error(f"Chyba při načítání dat: {e}")
        return

    if shutdown_requested:
        return

    # Extrakce příznaků s cachováním
    logger.info("Extrakce příznaků...")
    X_train = extract_features_batch(X_train_raw, cache, "train_features", config.batch_size)
    if X_train is None or shutdown_requested:
        return

    X_test = extract_features_batch(X_test_raw, cache, "test_features", config.batch_size)
    if X_test is None or shutdown_requested:
        return

    logger.info(f"Dimenze příznaků: {X_train.shape[1]} příznaků na vzorek")

    # Kontrola paměti před augmentací
    if not memory_monitor.check_memory():
        memory_monitor.force_gc()

    # Data augmentation
    if not shutdown_requested:
        smoter = SmoteRTransformer(
            augment_factor=config.augment_factor,
            noise_std=config.noise_std
        )
        X_train_aug, y_train_aug = smoter.fit_resample(X_train, y_train)

        if shutdown_requested:
            return
    else:
        return

    # Cross-validace na augmentovaných datech
    logger.info("Cross-validace finálního pipeline...")
    cv_splitter = KFold(n_splits=config.cv_folds, shuffle=True, random_state=config.random_state)
    mae_scorer = make_scorer(mean_absolute_error, greater_is_better=False)

    pipeline = create_final_pipeline(config)

    if not shutdown_requested:
        try:
            cv_scores = cross_val_score(
                pipeline, X_train_aug, y_train_aug,
                cv=cv_splitter, scoring=mae_scorer, n_jobs=config.n_jobs
            )

            cv_mae = -np.mean(cv_scores)
            cv_std = np.std(cv_scores)
            logger.info(f"Cross-validace MAE: {cv_mae:.2f} ± {cv_std:.2f} g")
        except Exception as e:
            logger.error(f"Chyba při cross-validaci: {e}")
            return
    else:
        return

    # Trénování finálního modelu
    if not shutdown_requested:
        logger.info("Trénování finálního modelu na všech datech...")
        try:
            start_time = time.time()
            pipeline.fit(X_train_aug, y_train_aug)
            training_time = time.time() - start_time
            logger.info(f"Trénování dokončeno za {training_time:.1f}s")
        except Exception as e:
            logger.error(f"Chyba při trénování: {e}")
            return
    else:
        return

    # Isotonic regression post-processing
    if not shutdown_requested:
        logger.info("Kalibrace isotonic regression...")
        try:
            train_pred = pipeline.predict(X_train)
            isotonic = IsotonicRegression(out_of_bounds='clip')
            isotonic.fit(train_pred, y_train)
        except Exception as e:
            logger.error(f"Chyba při kalibraci: {e}")
            return
    else:
        return

    # Finální predikce
    if not shutdown_requested:
        logger.info("Vytváření finálních predikcí...")
        try:
            test_pred_raw = pipeline.predict(X_test)
            test_pred_calibrated = isotonic.predict(test_pred_raw)
            final_predictions = np.clip(test_pred_calibrated, 0, 19200)
        except Exception as e:
            logger.error(f"Chyba při predikci: {e}")
            return
    else:
        return

    # Analýza predikcí
    logger.info("Analýza finálních predikcí...")
    logger.info(f"Rozsah: {final_predictions.min():.2f} - {final_predictions.max():.2f} g")
    logger.info(f"Průměr: {final_predictions.mean():.2f} g")
    logger.info(f"Std: {final_predictions.std():.2f} g")

    # Distribuce kolem trénovacích úrovní
    train_levels = [2500, 7500, 12500, 17500]
    logger.info("Distribuce predikcí kolem trénovacích úrovní:")
    for level in train_levels:
        close = np.sum(np.abs(final_predictions - level) < 500)
        logger.info(f"  ±500g od {level}g: {close} predikcí ({close/len(final_predictions)*100:.1f}%)")

    # Uložení výsledků
    logger.info("Ukládání výsledků...")
    try:
        # Uložení do results adresáře
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = config.results_dir / timestamp
        results_dir.mkdir(exist_ok=True)

        np.save(results_dir / 'predictions_final.npy', final_predictions)
        joblib.dump(pipeline, results_dir / 'final_pipeline.pkl')
        joblib.dump(isotonic, results_dir / 'isotonic_calibrator.pkl')

        # Také uložit do hlavního adresáře pro kompatibilitu
        np.save('predictions_final.npy', final_predictions)
        joblib.dump(pipeline, 'final_pipeline.pkl')
        joblib.dump(isotonic, 'isotonic_calibrator.pkl')

        logger.info(f"Výsledky uloženy do: {results_dir}")
    except Exception as e:
        logger.error(f"Chyba při ukládání: {e}")

    # Ukázka s nejistotou (pokud je GPR v ensemble)
    try:
        if hasattr(pipeline.named_steps['ensemble'], 'estimators_'):
            for name, est in pipeline.named_steps['ensemble'].estimators_:
                if name == 'gpr':
                    logger.info("Ukázka predikcí s nejistotou (prvních 5 vzorků):")
                    X_test_scaled = pipeline.named_steps['scaler'].transform(X_test[:5])
                    gpr_pred, gpr_std = est.predict(X_test_scaled, return_std=True)
                    for i in range(5):
                        logger.info(f"  Vzorek {i+1}: {gpr_pred[i]:.2f} ± {gpr_std[i]:.2f} g")
                    break
    except Exception as e:
        logger.warning(f"Chyba při výpočtu nejistoty: {e}")

    # Finální statistiky
    final_memory = psutil.virtual_memory()
    logger.info("="*70)
    logger.info("PIPELINE ÚSPĚŠNĚ DOKONČEN!")
    logger.info("="*70)
    logger.info(f"Finální využití paměti: {final_memory.percent:.1f}%")
    logger.info("Soubory:")
    logger.info("  - predictions_final.npy : Finální predikce")
    logger.info("  - final_pipeline.pkl : Natrénovaný model")
    logger.info("  - isotonic_calibrator.pkl : Kalibrátor pro post-processing")
    logger.info(f"  - logs/pipeline_*.log : Log soubor")


if __name__ == "__main__":
    main()