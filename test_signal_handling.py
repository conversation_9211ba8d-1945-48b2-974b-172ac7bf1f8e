#!/usr/bin/env python3
"""
Test script pro ověření signal handling v optimalizovaném pipeline.
"""

import signal
import sys
import time
import logging
from pathlib import Path

# Globální proměnné pro signal handling
shutdown_requested = False

def setup_logging():
    """Nastavení logování."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def signal_handler(signum, frame):
    """Handler pro graceful shutdown při Ctrl+C."""
    global shutdown_requested
    shutdown_requested = True
    logger = logging.getLogger(__name__)
    logger.warning(f"Přijat signal {signum}. Zahajuji graceful shutdown...")
    logger.info("Cleanup dokončen. Ukončuji program.")
    sys.exit(0)

def setup_signal_handlers():
    """Nastavení signal handlerů."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Test signal handling."""
    logger = setup_logging()
    setup_signal_handlers()
    
    logger.info("=== TEST SIGNAL HANDLING ===")
    logger.info("Stiskněte Ctrl+C pro test graceful shutdown")
    logger.info("Script bude běžet 60 sekund...")
    
    for i in range(60):
        if shutdown_requested:
            logger.info("Shutdown byl požadován, ukončuji...")
            break
        
        logger.info(f"Běžím... {i+1}/60 sekund")
        time.sleep(1)
    
    logger.info("Test dokončen!")

if __name__ == "__main__":
    main()
